import sqlite3
from datetime import datetime
from typing import Optional, Tuple
from contextlib import contextmanager
import pytz

class DatabaseHelper:
    def __init__(self, db_path: str = "amazon_prices.db"):
        self.db_path = db_path
        self.slovenia_tz = pytz.timezone('Europe/Ljubljana')
        self._init_db()

    def _get_slovenia_time(self) -> datetime:
        """Get current time in Slovenia timezone"""
        utc_now = datetime.now(pytz.UTC)
        return utc_now.astimezone(self.slovenia_tz)

    @contextmanager
    def get_connection(self):
        conn = sqlite3.connect(self.db_path)
        try:
            yield conn
        finally:
            conn.close()

    def _init_db(self):
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS products (
                    asin_code TEXT PRIMARY KEY,
                    title TEXT,
                    url TEXT
                )
            """)

            cursor.execute("""
                CREATE TABLE IF NOT EXISTS price_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    asin_code TEXT,
                    price REAL,
                    timestamp DATETIME,
                    notification_sent BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (asin_code) REFERENCES products (asin_code)
                )
            """)

            # Add notification_sent column to existing tables if it doesn't exist
            cursor.execute("PRAGMA table_info(price_history)")
            columns = [column[1] for column in cursor.fetchall()]
            if 'notification_sent' not in columns:
                cursor.execute("ALTER TABLE price_history ADD COLUMN notification_sent BOOLEAN DEFAULT FALSE")

            conn.commit()

    def save_product(self, product, notification_sent: bool = False) -> None:
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO products (asin_code, title, url)
                VALUES (?, ?, ?)
            """, (product.asin_code, product.title, product.url))

            # Convert price string to float, handling comma decimal separators
            try:
                price_str = product.price.replace("€", "").replace("$", "").strip()
                price_float = float(price_str.replace(",", "."))
            except (ValueError, AttributeError):
                price_float = 0.0

            cursor.execute("""
                INSERT INTO price_history (asin_code, price, timestamp, notification_sent)
                VALUES (?, ?, ?, ?)
            """, (product.asin_code, price_float, self._get_slovenia_time(), notification_sent))
            conn.commit()

    def get_price_history(self, asin_code: str) -> Optional[Tuple[float, float, float]]:
        """Returns (last_price, current_min_price, all_time_min_price) for an ASIN"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT price, timestamp FROM price_history
                WHERE asin_code = ?
                ORDER BY timestamp DESC
                LIMIT 2
            """, (asin_code,))
            results = cursor.fetchall()

            if not results:
                return None

            current_price = results[0][0]
            last_price = results[1][0] if len(results) > 1 else current_price

            # Get all-time minimum price
            cursor.execute("""
                SELECT MIN(price) FROM price_history
                WHERE asin_code = ?
            """, (asin_code,))
            all_time_min = cursor.fetchone()[0]

            return last_price, current_price, all_time_min

    def has_notification_been_sent_for_current_price(self, asin_code: str, current_price: float) -> bool:
        """Check if a notification has already been sent for the current price level"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) FROM price_history
                WHERE asin_code = ? AND ABS(price - ?) < 0.01 AND notification_sent = TRUE
            """, (asin_code, current_price))
            count = cursor.fetchone()[0]
            return count > 0

    def mark_notification_sent(self, asin_code: str, price: float) -> None:
        """Mark that a notification has been sent for a specific price"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE price_history
                SET notification_sent = TRUE
                WHERE asin_code = ? AND ABS(price - ?) < 0.01 AND notification_sent = FALSE
            """, (asin_code, price))
            conn.commit()